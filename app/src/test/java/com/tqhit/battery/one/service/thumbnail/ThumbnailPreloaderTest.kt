package com.tqhit.battery.one.service.thumbnail

import android.content.Context
import com.tqhit.battery.one.fragment.main.animation.data.ThumbnailItem
import com.tqhit.battery.one.fragment.main.animation.data.ThumbnailPreloadResult
import com.tqhit.battery.one.fragment.main.animation.data.ThumbnailPreloadStatus
import com.tqhit.battery.one.fragment.main.animation.data.PreloadedThumbnailItem
import com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import java.io.File

/**
 * Unit tests for ThumbnailPreloader.
 * Tests thumbnail preloading functionality with proper mocking and edge case coverage.
 */
class ThumbnailPreloaderTest {
    
    private lateinit var thumbnailPreloader: ThumbnailPreloader
    private lateinit var mockContext: Context
    private lateinit var mockFileManager: ThumbnailFileManager
    private lateinit var testThumbnails: List<ThumbnailItem>
    
    @Before
    fun setUp() {
        // Arrange - Set up mocks and test subject
        mockContext = mockk(relaxed = true)
        mockFileManager = mockk(relaxed = true)
        
        thumbnailPreloader = ThumbnailPreloader(mockContext, mockFileManager)
        
        // Create test thumbnail data
        testThumbnails = listOf(
            ThumbnailItem(
                thumbnailUrl = "https://example.com/anime1_thumb.jpg",
                categoryName = "Anime",
                animationMediaUrl = "https://example.com/anime1.mp4",
                isPremium = false
            ),
            ThumbnailItem(
                thumbnailUrl = "https://example.com/cartoon1_thumb.jpg",
                categoryName = "Cartoon",
                animationMediaUrl = "https://example.com/cartoon1.mp4",
                isPremium = true
            ),
            ThumbnailItem(
                thumbnailUrl = "https://example.com/anime2_thumb.png",
                categoryName = "Anime",
                animationMediaUrl = "https://example.com/anime2.mp4",
                isPremium = false
            )
        )
    }
    
    @Test
    fun `preloadThumbnails with empty list returns empty result`() = runTest {
        // Act
        val actualResults = thumbnailPreloader.preloadThumbnails(emptyList())
        
        // Assert
        assertTrue("Should return empty list for empty input", actualResults.isEmpty())
    }
    
    @Test
    fun `preloadThumbnails with valid thumbnails processes all items`() = runTest {
        // Arrange
        val mockFile = mockk<File>(relaxed = true)
        every { mockFile.exists() } returns false
        coEvery { mockFileManager.getPreloadedThumbnail(any(), any(), any()) } returns null
        coEvery { mockFileManager.createThumbnailFile(any()) } returns mockFile
        coEvery { mockFileManager.validateDownloadedThumbnail(any()) } returns true
        every { mockFile.absolutePath } returns "/mock/path/thumbnail.jpg"
        every { mockFile.length() } returns 1024L
        
        // Act
        val actualResults = thumbnailPreloader.preloadThumbnails(testThumbnails)
        
        // Assert
        assertEquals("Should process all thumbnails", testThumbnails.size, actualResults.size)
        coVerify(exactly = testThumbnails.size) { mockFileManager.createThumbnailFile(any()) }
    }
    
    @Test
    fun `preloadThumbnails with already existing thumbnail returns AlreadyExists`() = runTest {
        // Arrange
        val existingThumbnail = PreloadedThumbnailItem(
            thumbnailUrl = testThumbnails[0].thumbnailUrl,
            localFilePath = "/existing/path/thumbnail.jpg",
            status = ThumbnailPreloadStatus.COMPLETED,
            downloadTimestamp = System.currentTimeMillis(),
            fileSizeBytes = 2048L,
            categoryName = "Anime",
            animationMediaUrl = testThumbnails[0].animationMediaUrl
        )
        
        coEvery { mockFileManager.getPreloadedThumbnail(testThumbnails[0].thumbnailUrl, any(), any()) } returns existingThumbnail
        
        // Act
        val actualResults = thumbnailPreloader.preloadThumbnails(listOf(testThumbnails[0]))
        
        // Assert
        assertEquals("Should return one result", 1, actualResults.size)
        assertTrue("Should return AlreadyExists result", actualResults[0] is ThumbnailPreloadResult.AlreadyExists)
        
        val alreadyExistsResult = actualResults[0] as ThumbnailPreloadResult.AlreadyExists
        assertEquals("Should return existing thumbnail", existingThumbnail, alreadyExistsResult.preloadedThumbnail)
    }
    
    @Test
    fun `preloadThumbnails with invalid URL returns Failure`() = runTest {
        // Arrange
        val invalidThumbnail = ThumbnailItem(
            thumbnailUrl = "invalid-url",
            categoryName = "Test",
            animationMediaUrl = "https://example.com/test.mp4",
            isPremium = false
        )
        
        coEvery { mockFileManager.getPreloadedThumbnail(any(), any(), any()) } returns null
        
        // Act
        val actualResults = thumbnailPreloader.preloadThumbnails(listOf(invalidThumbnail))
        
        // Assert
        assertEquals("Should return one result", 1, actualResults.size)
        assertTrue("Should return Failure result", actualResults[0] is ThumbnailPreloadResult.Failure)
        
        val failureResult = actualResults[0] as ThumbnailPreloadResult.Failure
        assertEquals("Should have correct thumbnail URL", invalidThumbnail.thumbnailUrl, failureResult.thumbnailUrl)
        assertTrue("Should contain error message about invalid URL", failureResult.errorMessage.contains("Invalid"))
    }
    
    @Test
    fun `isThumbnailPreloaded returns true for existing valid thumbnail`() = runTest {
        // Arrange
        val thumbnailUrl = "https://example.com/test_thumb.jpg"
        val existingThumbnail = PreloadedThumbnailItem(
            thumbnailUrl = thumbnailUrl,
            localFilePath = "/existing/path/thumbnail.jpg",
            status = ThumbnailPreloadStatus.COMPLETED,
            downloadTimestamp = System.currentTimeMillis(),
            fileSizeBytes = 1024L,
            categoryName = "Test",
            animationMediaUrl = "https://example.com/test.mp4"
        )
        
        val mockFile = mockk<File>()
        every { mockFile.exists() } returns true
        
        coEvery { mockFileManager.getPreloadedThumbnail(thumbnailUrl) } returns existingThumbnail
        
        // Mock File constructor
        every { File(existingThumbnail.localFilePath) } returns mockFile
        
        // Act
        val actualResult = thumbnailPreloader.isThumbnailPreloaded(thumbnailUrl)
        
        // Assert
        assertTrue("Should return true for existing valid thumbnail", actualResult)
    }
    
    @Test
    fun `isThumbnailPreloaded returns false for non-existing thumbnail`() = runTest {
        // Arrange
        val thumbnailUrl = "https://example.com/nonexistent_thumb.jpg"
        coEvery { mockFileManager.getPreloadedThumbnail(thumbnailUrl) } returns null
        
        // Act
        val actualResult = thumbnailPreloader.isThumbnailPreloaded(thumbnailUrl)
        
        // Assert
        assertFalse("Should return false for non-existing thumbnail", actualResult)
    }
    
    @Test
    fun `getPreloadedThumbnailPath returns correct path for existing thumbnail`() = runTest {
        // Arrange
        val thumbnailUrl = "https://example.com/test_thumb.jpg"
        val expectedPath = "/existing/path/thumbnail.jpg"
        val existingThumbnail = PreloadedThumbnailItem(
            thumbnailUrl = thumbnailUrl,
            localFilePath = expectedPath,
            status = ThumbnailPreloadStatus.COMPLETED,
            downloadTimestamp = System.currentTimeMillis(),
            fileSizeBytes = 1024L,
            categoryName = "Test",
            animationMediaUrl = "https://example.com/test.mp4"
        )
        
        val mockFile = mockk<File>()
        every { mockFile.exists() } returns true
        every { File(expectedPath) } returns mockFile
        
        coEvery { mockFileManager.getPreloadedThumbnail(thumbnailUrl) } returns existingThumbnail
        
        // Act
        val actualPath = thumbnailPreloader.getPreloadedThumbnailPath(thumbnailUrl)
        
        // Assert
        assertEquals("Should return correct path", expectedPath, actualPath)
    }
    
    @Test
    fun `getPreloadedThumbnailPath returns null for non-existing thumbnail`() = runTest {
        // Arrange
        val thumbnailUrl = "https://example.com/nonexistent_thumb.jpg"
        coEvery { mockFileManager.getPreloadedThumbnail(thumbnailUrl) } returns null
        
        // Act
        val actualPath = thumbnailPreloader.getPreloadedThumbnailPath(thumbnailUrl)
        
        // Assert
        assertNull("Should return null for non-existing thumbnail", actualPath)
    }
    
    @Test
    fun `preloadThumbnails handles concurrent downloads properly`() = runTest {
        // Arrange
        val largeThumbnailList = (1..10).map { index ->
            ThumbnailItem(
                thumbnailUrl = "https://example.com/thumb$index.jpg",
                categoryName = "Test",
                animationMediaUrl = "https://example.com/video$index.mp4",
                isPremium = false
            )
        }
        
        val mockFile = mockk<File>(relaxed = true)
        every { mockFile.exists() } returns false
        every { mockFile.absolutePath } returns "/mock/path/thumbnail.jpg"
        every { mockFile.length() } returns 1024L
        
        coEvery { mockFileManager.getPreloadedThumbnail(any(), any(), any()) } returns null
        coEvery { mockFileManager.createThumbnailFile(any()) } returns mockFile
        coEvery { mockFileManager.validateDownloadedThumbnail(any()) } returns true
        
        // Act
        val actualResults = thumbnailPreloader.preloadThumbnails(largeThumbnailList)
        
        // Assert
        assertEquals("Should process all thumbnails", largeThumbnailList.size, actualResults.size)
        // Verify that concurrent downloads were managed (semaphore should limit concurrent operations)
        coVerify(exactly = largeThumbnailList.size) { mockFileManager.createThumbnailFile(any()) }
    }
}
