package com.tqhit.battery.one.manager.thumbnail

import android.content.Context
import com.tqhit.battery.one.fragment.main.animation.data.ThumbnailPreloadStatus
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import java.io.File

/**
 * Unit tests for ThumbnailFileManager.
 * Tests file management operations for thumbnail preloading.
 */
class ThumbnailFileManagerTest {
    
    private lateinit var thumbnailFileManager: ThumbnailFileManager
    private lateinit var mockContext: Context
    private lateinit var mockFilesDir: File
    
    @Before
    fun setUp() {
        // Arrange - Set up mocks and test subject
        mockContext = mockk(relaxed = true)
        mockFilesDir = mockk(relaxed = true)
        
        every { mockContext.filesDir } returns mockFilesDir
        every { mockFilesDir.absolutePath } returns "/mock/files"
        
        thumbnailFileManager = ThumbnailFileManager(mockContext)
    }
    
    @Test
    fun `generateThumbnailFileName creates consistent filename for same URL`() {
        // Arrange
        val thumbnailUrl = "https://example.com/test_thumbnail.jpg"
        
        // Act
        val actualFileName1 = thumbnailFileManager.generateThumbnailFileName(thumbnailUrl)
        val actualFileName2 = thumbnailFileManager.generateThumbnailFileName(thumbnailUrl)
        
        // Assert
        assertEquals("Should generate consistent filename", actualFileName1, actualFileName2)
        assertTrue("Should end with .jpg extension", actualFileName1.endsWith(".jpg"))
        assertFalse("Should not be empty", actualFileName1.isEmpty())
    }
    
    @Test
    fun `generateThumbnailFileName handles different image extensions`() {
        // Arrange
        val jpegUrl = "https://example.com/test.jpeg"
        val pngUrl = "https://example.com/test.png"
        val webpUrl = "https://example.com/test.webp"
        
        // Act
        val jpegFileName = thumbnailFileManager.generateThumbnailFileName(jpegUrl)
        val pngFileName = thumbnailFileManager.generateThumbnailFileName(pngUrl)
        val webpFileName = thumbnailFileManager.generateThumbnailFileName(webpUrl)
        
        // Assert
        assertTrue("JPEG URL should generate .jpeg filename", jpegFileName.endsWith(".jpeg"))
        assertTrue("PNG URL should generate .png filename", pngFileName.endsWith(".png"))
        assertTrue("WebP URL should generate .webp filename", webpFileName.endsWith(".webp"))
    }
    
    @Test
    fun `generateThumbnailFileName defaults to jpg for unknown extension`() {
        // Arrange
        val unknownUrl = "https://example.com/test.unknown"
        
        // Act
        val actualFileName = thumbnailFileManager.generateThumbnailFileName(unknownUrl)
        
        // Assert
        assertTrue("Should default to .jpg extension", actualFileName.endsWith(".jpg"))
    }
    
    @Test
    fun `generateThumbnailFileName creates different filenames for different URLs`() {
        // Arrange
        val url1 = "https://example.com/thumbnail1.jpg"
        val url2 = "https://example.com/thumbnail2.jpg"
        
        // Act
        val fileName1 = thumbnailFileManager.generateThumbnailFileName(url1)
        val fileName2 = thumbnailFileManager.generateThumbnailFileName(url2)
        
        // Assert
        assertNotEquals("Should generate different filenames for different URLs", fileName1, fileName2)
    }
    
    @Test
    fun `createThumbnailFile returns File with correct path`() {
        // Arrange
        val thumbnailUrl = "https://example.com/test_thumbnail.jpg"
        val mockThumbnailDir = mockk<File>(relaxed = true)
        
        every { File(mockFilesDir, "preloaded_thumbnails") } returns mockThumbnailDir
        every { mockThumbnailDir.exists() } returns true
        
        // Act
        val actualFile = thumbnailFileManager.createThumbnailFile(thumbnailUrl)
        
        // Assert
        assertNotNull("Should return a File object", actualFile)
        // Verify the file is created in the correct directory structure
        assertTrue("File path should contain thumbnail directory", actualFile.toString().contains("preloaded_thumbnails"))
    }
    
    @Test
    fun `getPreloadedThumbnail returns null for non-existing file`() = runTest {
        // Arrange
        val thumbnailUrl = "https://example.com/nonexistent_thumbnail.jpg"
        val mockThumbnailDir = mockk<File>(relaxed = true)
        val mockThumbnailFile = mockk<File>(relaxed = true)
        
        every { File(mockFilesDir, "preloaded_thumbnails") } returns mockThumbnailDir
        every { mockThumbnailDir.exists() } returns true
        every { File(mockThumbnailDir, any<String>()) } returns mockThumbnailFile
        every { mockThumbnailFile.exists() } returns false
        
        // Act
        val actualResult = thumbnailFileManager.getPreloadedThumbnail(thumbnailUrl)
        
        // Assert
        assertNull("Should return null for non-existing file", actualResult)
    }
    
    @Test
    fun `getPreloadedThumbnail returns PreloadedThumbnailItem for existing file`() = runTest {
        // Arrange
        val thumbnailUrl = "https://example.com/existing_thumbnail.jpg"
        val categoryName = "Anime"
        val animationMediaUrl = "https://example.com/animation.mp4"
        val mockThumbnailDir = mockk<File>(relaxed = true)
        val mockThumbnailFile = mockk<File>(relaxed = true)
        val expectedTimestamp = System.currentTimeMillis()
        val expectedSize = 2048L
        
        every { File(mockFilesDir, "preloaded_thumbnails") } returns mockThumbnailDir
        every { mockThumbnailDir.exists() } returns true
        every { File(mockThumbnailDir, any<String>()) } returns mockThumbnailFile
        every { mockThumbnailFile.exists() } returns true
        every { mockThumbnailFile.isFile } returns true
        every { mockThumbnailFile.length() } returns expectedSize
        every { mockThumbnailFile.lastModified() } returns expectedTimestamp
        every { mockThumbnailFile.absolutePath } returns "/mock/path/thumbnail.jpg"
        
        // Act
        val actualResult = thumbnailFileManager.getPreloadedThumbnail(thumbnailUrl, categoryName, animationMediaUrl)
        
        // Assert
        assertNotNull("Should return PreloadedThumbnailItem for existing file", actualResult)
        assertEquals("Should have correct thumbnail URL", thumbnailUrl, actualResult?.thumbnailUrl)
        assertEquals("Should have correct category name", categoryName, actualResult?.categoryName)
        assertEquals("Should have correct animation media URL", animationMediaUrl, actualResult?.animationMediaUrl)
        assertEquals("Should have correct file size", expectedSize, actualResult?.fileSizeBytes)
        assertEquals("Should have COMPLETED status", ThumbnailPreloadStatus.COMPLETED, actualResult?.status)
    }
    
    @Test
    fun `validateDownloadedThumbnail returns false for non-existing file`() = runTest {
        // Arrange
        val mockFile = mockk<File>(relaxed = true)
        every { mockFile.exists() } returns false
        
        // Act
        val actualResult = thumbnailFileManager.validateDownloadedThumbnail(mockFile)
        
        // Assert
        assertFalse("Should return false for non-existing file", actualResult)
    }
    
    @Test
    fun `validateDownloadedThumbnail returns false for empty file`() = runTest {
        // Arrange
        val mockFile = mockk<File>(relaxed = true)
        every { mockFile.exists() } returns true
        every { mockFile.isFile } returns true
        every { mockFile.length() } returns 0L
        
        // Act
        val actualResult = thumbnailFileManager.validateDownloadedThumbnail(mockFile)
        
        // Assert
        assertFalse("Should return false for empty file", actualResult)
    }
    
    @Test
    fun `getPreloadedThumbnailCount returns zero for empty directory`() = runTest {
        // Arrange
        val mockThumbnailDir = mockk<File>(relaxed = true)
        every { File(mockFilesDir, "preloaded_thumbnails") } returns mockThumbnailDir
        every { mockThumbnailDir.exists() } returns true
        every { mockThumbnailDir.listFiles() } returns emptyArray()
        
        // Act
        val actualCount = thumbnailFileManager.getPreloadedThumbnailCount()
        
        // Assert
        assertEquals("Should return zero for empty directory", 0, actualCount)
    }
    
    @Test
    fun `getTotalThumbnailSize returns zero for empty directory`() = runTest {
        // Arrange
        val mockThumbnailDir = mockk<File>(relaxed = true)
        every { File(mockFilesDir, "preloaded_thumbnails") } returns mockThumbnailDir
        every { mockThumbnailDir.exists() } returns true
        every { mockThumbnailDir.listFiles() } returns emptyArray()
        
        // Act
        val actualSize = thumbnailFileManager.getTotalThumbnailSize()
        
        // Assert
        assertEquals("Should return zero for empty directory", 0L, actualSize)
    }
    
    @Test
    fun `getTotalThumbnailSize calculates correct total for multiple files`() = runTest {
        // Arrange
        val mockThumbnailDir = mockk<File>(relaxed = true)
        val mockFile1 = mockk<File>(relaxed = true)
        val mockFile2 = mockk<File>(relaxed = true)
        val mockFile3 = mockk<File>(relaxed = true)
        
        every { File(mockFilesDir, "preloaded_thumbnails") } returns mockThumbnailDir
        every { mockThumbnailDir.exists() } returns true
        every { mockThumbnailDir.listFiles() } returns arrayOf(mockFile1, mockFile2, mockFile3)
        
        every { mockFile1.isFile } returns true
        every { mockFile1.length() } returns 1024L
        every { mockFile2.isFile } returns true
        every { mockFile2.length() } returns 2048L
        every { mockFile3.isFile } returns false // Directory, should be ignored
        every { mockFile3.length() } returns 0L
        
        // Act
        val actualSize = thumbnailFileManager.getTotalThumbnailSize()
        
        // Assert
        assertEquals("Should calculate correct total size", 3072L, actualSize)
    }
}
