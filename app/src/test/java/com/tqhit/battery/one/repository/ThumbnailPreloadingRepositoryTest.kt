package com.tqhit.battery.one.repository

import com.google.gson.Gson
import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import com.tqhit.battery.one.fragment.main.animation.data.PreloadedThumbnailItem
import com.tqhit.battery.one.fragment.main.animation.data.ThumbnailItem
import com.tqhit.battery.one.fragment.main.animation.data.ThumbnailPreloadResult
import com.tqhit.battery.one.fragment.main.animation.data.ThumbnailPreloadStatus
import com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager
import com.tqhit.battery.one.service.thumbnail.ThumbnailPreloader
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for ThumbnailPreloadingRepository.
 * Tests repository pattern for thumbnail preloading operations.
 */
class ThumbnailPreloadingRepositoryTest {
    
    private lateinit var thumbnailPreloadingRepository: ThumbnailPreloadingRepository
    private lateinit var mockThumbnailPreloader: ThumbnailPreloader
    private lateinit var mockFileManager: ThumbnailFileManager
    private lateinit var mockPreferencesHelper: PreferencesHelper
    private lateinit var mockGson: Gson
    private lateinit var testThumbnails: List<ThumbnailItem>
    
    @Before
    fun setUp() {
        // Arrange - Set up mocks and test subject
        mockThumbnailPreloader = mockk(relaxed = true)
        mockFileManager = mockk(relaxed = true)
        mockPreferencesHelper = mockk(relaxed = true)
        mockGson = mockk(relaxed = true)
        
        thumbnailPreloadingRepository = ThumbnailPreloadingRepository(
            mockThumbnailPreloader,
            mockFileManager,
            mockPreferencesHelper,
            mockGson
        )
        
        // Create test thumbnail data
        testThumbnails = listOf(
            ThumbnailItem(
                thumbnailUrl = "https://example.com/anime1_thumb.jpg",
                categoryName = "Anime",
                animationMediaUrl = "https://example.com/anime1.mp4",
                isPremium = false
            ),
            ThumbnailItem(
                thumbnailUrl = "https://example.com/cartoon1_thumb.jpg",
                categoryName = "Cartoon",
                animationMediaUrl = "https://example.com/cartoon1.mp4",
                isPremium = true
            )
        )
    }
    
    @Test
    fun `initiatePreloading with empty list returns NoThumbnailsProvided`() = runTest {
        // Act
        val actualResult = thumbnailPreloadingRepository.initiatePreloading(emptyList())
        
        // Assert
        assertTrue("Should return NoThumbnailsProvided for empty list", 
            actualResult is ThumbnailPreloadingResult.NoThumbnailsProvided)
    }
    
    @Test
    fun `initiatePreloading with successful results returns Success`() = runTest {
        // Arrange
        val successResults = testThumbnails.map { thumbnail ->
            ThumbnailPreloadResult.Success(
                PreloadedThumbnailItem(
                    thumbnailUrl = thumbnail.thumbnailUrl,
                    localFilePath = "/mock/path/${thumbnail.thumbnailUrl.hashCode()}.jpg",
                    status = ThumbnailPreloadStatus.COMPLETED,
                    downloadTimestamp = System.currentTimeMillis(),
                    fileSizeBytes = 1024L,
                    categoryName = thumbnail.categoryName,
                    animationMediaUrl = thumbnail.animationMediaUrl
                )
            )
        }
        
        coEvery { mockThumbnailPreloader.preloadThumbnails(testThumbnails) } returns successResults
        every { mockGson.toJson(any<List<PreloadedThumbnailItem>>()) } returns "[]"
        every { mockPreferencesHelper.getString("preloaded_thumbnails", "") } returns ""
        
        // Act
        val actualResult = thumbnailPreloadingRepository.initiatePreloading(testThumbnails)
        
        // Assert
        assertTrue("Should return Success result", actualResult is ThumbnailPreloadingResult.Success)
        
        val successResult = actualResult as ThumbnailPreloadingResult.Success
        assertEquals("Should have correct successful count", 2, successResult.successfulCount)
        assertEquals("Should have zero existing count", 0, successResult.existingCount)
        
        // Verify interactions
        coVerify(exactly = 1) { mockThumbnailPreloader.preloadThumbnails(testThumbnails) }
        verify(exactly = 1) { mockPreferencesHelper.saveLong("last_thumbnail_preload_timestamp", any()) }
    }
    
    @Test
    fun `initiatePreloading with mixed results returns PartialSuccess`() = runTest {
        // Arrange
        val mixedResults = listOf(
            ThumbnailPreloadResult.Success(
                PreloadedThumbnailItem(
                    thumbnailUrl = testThumbnails[0].thumbnailUrl,
                    localFilePath = "/mock/path/success.jpg",
                    status = ThumbnailPreloadStatus.COMPLETED,
                    downloadTimestamp = System.currentTimeMillis(),
                    fileSizeBytes = 1024L,
                    categoryName = testThumbnails[0].categoryName,
                    animationMediaUrl = testThumbnails[0].animationMediaUrl
                )
            ),
            ThumbnailPreloadResult.Failure(
                thumbnailUrl = testThumbnails[1].thumbnailUrl,
                errorMessage = "Download failed",
                exception = null
            )
        )
        
        coEvery { mockThumbnailPreloader.preloadThumbnails(testThumbnails) } returns mixedResults
        every { mockGson.toJson(any<List<PreloadedThumbnailItem>>()) } returns "[]"
        every { mockPreferencesHelper.getString("preloaded_thumbnails", "") } returns ""
        
        // Act
        val actualResult = thumbnailPreloadingRepository.initiatePreloading(testThumbnails)
        
        // Assert
        assertTrue("Should return PartialSuccess result", actualResult is ThumbnailPreloadingResult.PartialSuccess)
        
        val partialResult = actualResult as ThumbnailPreloadingResult.PartialSuccess
        assertEquals("Should have correct successful count", 1, partialResult.successfulCount)
        assertEquals("Should have correct failed count", 1, partialResult.failedCount)
        assertEquals("Should have zero existing count", 0, partialResult.existingCount)
    }
    
    @Test
    fun `initiatePreloading with all failures returns AllFailed`() = runTest {
        // Arrange
        val failureResults = testThumbnails.map { thumbnail ->
            ThumbnailPreloadResult.Failure(
                thumbnailUrl = thumbnail.thumbnailUrl,
                errorMessage = "Download failed",
                exception = null
            )
        }
        
        coEvery { mockThumbnailPreloader.preloadThumbnails(testThumbnails) } returns failureResults
        
        // Act
        val actualResult = thumbnailPreloadingRepository.initiatePreloading(testThumbnails)
        
        // Assert
        assertTrue("Should return AllFailed result", actualResult is ThumbnailPreloadingResult.AllFailed)
        
        val allFailedResult = actualResult as ThumbnailPreloadingResult.AllFailed
        assertEquals("Should have correct number of failed results", 2, allFailedResult.failedResults.size)
    }
    
    @Test
    fun `getPreloadedThumbnail delegates to file manager`() = runTest {
        // Arrange
        val thumbnailUrl = "https://example.com/test_thumb.jpg"
        val expectedThumbnail = PreloadedThumbnailItem(
            thumbnailUrl = thumbnailUrl,
            localFilePath = "/mock/path/thumbnail.jpg",
            status = ThumbnailPreloadStatus.COMPLETED,
            downloadTimestamp = System.currentTimeMillis(),
            fileSizeBytes = 1024L,
            categoryName = "Test",
            animationMediaUrl = "https://example.com/test.mp4"
        )
        
        coEvery { mockFileManager.getPreloadedThumbnail(thumbnailUrl) } returns expectedThumbnail
        
        // Act
        val actualResult = thumbnailPreloadingRepository.getPreloadedThumbnail(thumbnailUrl)
        
        // Assert
        assertEquals("Should return thumbnail from file manager", expectedThumbnail, actualResult)
        coVerify(exactly = 1) { mockFileManager.getPreloadedThumbnail(thumbnailUrl) }
    }
    
    @Test
    fun `isThumbnailPreloaded delegates to thumbnail preloader`() = runTest {
        // Arrange
        val thumbnailUrl = "https://example.com/test_thumb.jpg"
        coEvery { mockThumbnailPreloader.isThumbnailPreloaded(thumbnailUrl) } returns true
        
        // Act
        val actualResult = thumbnailPreloadingRepository.isThumbnailPreloaded(thumbnailUrl)
        
        // Assert
        assertTrue("Should return true from thumbnail preloader", actualResult)
        coVerify(exactly = 1) { mockThumbnailPreloader.isThumbnailPreloaded(thumbnailUrl) }
    }
    
    @Test
    fun `getPreloadedThumbnailPath delegates to thumbnail preloader`() = runTest {
        // Arrange
        val thumbnailUrl = "https://example.com/test_thumb.jpg"
        val expectedPath = "/mock/path/thumbnail.jpg"
        coEvery { mockThumbnailPreloader.getPreloadedThumbnailPath(thumbnailUrl) } returns expectedPath
        
        // Act
        val actualResult = thumbnailPreloadingRepository.getPreloadedThumbnailPath(thumbnailUrl)
        
        // Assert
        assertEquals("Should return path from thumbnail preloader", expectedPath, actualResult)
        coVerify(exactly = 1) { mockThumbnailPreloader.getPreloadedThumbnailPath(thumbnailUrl) }
    }
    
    @Test
    fun `getThumbnailPreloadingStats returns correct statistics`() = runTest {
        // Arrange
        val expectedCount = 5
        val expectedSize = 10240L
        val expectedTimestamp = System.currentTimeMillis()
        
        coEvery { mockFileManager.getPreloadedThumbnailCount() } returns expectedCount
        coEvery { mockFileManager.getTotalThumbnailSize() } returns expectedSize
        every { mockPreferencesHelper.getLong("last_thumbnail_preload_timestamp", 0L) } returns expectedTimestamp
        
        // Act
        val actualStats = thumbnailPreloadingRepository.getThumbnailPreloadingStats()
        
        // Assert
        assertEquals("Should have correct preloaded count", expectedCount, actualStats.preloadedCount)
        assertEquals("Should have correct total size", expectedSize, actualStats.totalSizeBytes)
        assertEquals("Should have correct timestamp", expectedTimestamp, actualStats.lastPreloadTimestamp)
    }
    
    @Test
    fun `cleanupThumbnailFiles delegates to file manager and returns count`() = runTest {
        // Arrange
        val expectedCleanedCount = 3
        coEvery { mockFileManager.cleanupThumbnailFiles() } returns expectedCleanedCount
        
        // Act
        val actualCount = thumbnailPreloadingRepository.cleanupThumbnailFiles()
        
        // Assert
        assertEquals("Should return cleaned count from file manager", expectedCleanedCount, actualCount)
        coVerify(exactly = 1) { mockFileManager.cleanupThumbnailFiles() }
    }
    
    @Test
    fun `initiatePreloading handles exceptions and returns Error result`() = runTest {
        // Arrange
        val expectedException = RuntimeException("Test exception")
        coEvery { mockThumbnailPreloader.preloadThumbnails(testThumbnails) } throws expectedException
        
        // Act
        val actualResult = thumbnailPreloadingRepository.initiatePreloading(testThumbnails)
        
        // Assert
        assertTrue("Should return Error result for exceptions", actualResult is ThumbnailPreloadingResult.Error)
        
        val errorResult = actualResult as ThumbnailPreloadingResult.Error
        assertEquals("Should contain the original exception", expectedException, errorResult.exception)
    }
}
