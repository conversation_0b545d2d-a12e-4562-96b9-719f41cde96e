package com.tqhit.battery.one.service.thumbnail

import com.tqhit.battery.one.fragment.main.animation.data.AnimationCategory
import com.tqhit.battery.one.fragment.main.animation.data.AnimationItem
import com.tqhit.battery.one.service.animation.AnimationDataService
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for ThumbnailDataService.
 * Tests thumbnail data extraction from animation categories.
 */
class ThumbnailDataServiceTest {
    
    private lateinit var thumbnailDataService: ThumbnailDataService
    private lateinit var mockAnimationDataService: AnimationDataService
    private lateinit var testCategories: List<AnimationCategory>
    
    @Before
    fun setUp() {
        // Arrange - Set up mocks and test subject
        mockAnimationDataService = mockk(relaxed = true)
        thumbnailDataService = ThumbnailDataService(mockAnimationDataService)
        
        // Create test animation categories
        testCategories = listOf(
            AnimationCategory(
                name = "Anime",
                content = listOf(
                    AnimationItem(
                        isPremium = false,
                        mediaOriginal = "https://example.com/anime1.mp4",
                        thumbnail = "https://example.com/anime1_thumb.jpg"
                    ),
                    AnimationItem(
                        isPremium = true,
                        mediaOriginal = "https://example.com/anime2.mp4",
                        thumbnail = "https://example.com/anime2_thumb.png"
                    ),
                    AnimationItem(
                        isPremium = false,
                        mediaOriginal = "https://example.com/anime3.mp4",
                        thumbnail = "https://example.com/anime3_thumb.jpg"
                    ),
                    AnimationItem(
                        isPremium = false,
                        mediaOriginal = "https://example.com/anime4.mp4",
                        thumbnail = "https://example.com/anime4_thumb.jpg"
                    ),
                    AnimationItem(
                        isPremium = false,
                        mediaOriginal = "https://example.com/anime5.mp4",
                        thumbnail = "https://example.com/anime5_thumb.jpg"
                    )
                )
            ),
            AnimationCategory(
                name = "Cartoon",
                content = listOf(
                    AnimationItem(
                        isPremium = false,
                        mediaOriginal = "https://example.com/cartoon1.mp4",
                        thumbnail = "https://example.com/cartoon1_thumb.jpg"
                    ),
                    AnimationItem(
                        isPremium = true,
                        mediaOriginal = "https://example.com/cartoon2.mp4",
                        thumbnail = "https://example.com/cartoon2_thumb.webp"
                    ),
                    AnimationItem(
                        isPremium = false,
                        mediaOriginal = "https://example.com/cartoon3.mp4",
                        thumbnail = "https://example.com/cartoon3_thumb.jpg"
                    ),
                    AnimationItem(
                        isPremium = false,
                        mediaOriginal = "https://example.com/cartoon4.mp4",
                        thumbnail = "https://example.com/cartoon4_thumb.jpg"
                    )
                )
            ),
            AnimationCategory(
                name = "Other",
                content = listOf(
                    AnimationItem(
                        isPremium = false,
                        mediaOriginal = "https://example.com/other1.mp4",
                        thumbnail = "https://example.com/other1_thumb.jpg"
                    )
                )
            )
        )
    }
    
    @Test
    fun `getThumbnailsForPreloading returns correct number of thumbnails from target categories`() = runTest {
        // Arrange
        coEvery { mockAnimationDataService.getAllAnimationCategories() } returns testCategories
        
        // Act
        val actualThumbnails = thumbnailDataService.getThumbnailsForPreloading()
        
        // Assert
        assertEquals("Should return 8 thumbnails (4 from Anime + 4 from Cartoon)", 8, actualThumbnails.size)
        
        // Verify category distribution
        val animeCount = actualThumbnails.count { it.categoryName == "Anime" }
        val cartoonCount = actualThumbnails.count { it.categoryName == "Cartoon" }
        val otherCount = actualThumbnails.count { it.categoryName == "Other" }
        
        assertEquals("Should have 4 Anime thumbnails", 4, animeCount)
        assertEquals("Should have 4 Cartoon thumbnails", 4, cartoonCount)
        assertEquals("Should have 0 Other thumbnails", 0, otherCount)
    }
    
    @Test
    fun `getThumbnailsForPreloading handles empty categories`() = runTest {
        // Arrange
        coEvery { mockAnimationDataService.getAllAnimationCategories() } returns emptyList()
        
        // Act
        val actualThumbnails = thumbnailDataService.getThumbnailsForPreloading()
        
        // Assert
        assertTrue("Should return empty list for empty categories", actualThumbnails.isEmpty())
    }
    
    @Test
    fun `getThumbnailsForPreloading filters out invalid thumbnail URLs`() = runTest {
        // Arrange
        val categoriesWithInvalidThumbnails = listOf(
            AnimationCategory(
                name = "Anime",
                content = listOf(
                    AnimationItem(
                        isPremium = false,
                        mediaOriginal = "https://example.com/anime1.mp4",
                        thumbnail = "https://example.com/anime1_thumb.jpg" // Valid
                    ),
                    AnimationItem(
                        isPremium = false,
                        mediaOriginal = "https://example.com/anime2.mp4",
                        thumbnail = "invalid-url" // Invalid
                    ),
                    AnimationItem(
                        isPremium = false,
                        mediaOriginal = "https://example.com/anime3.mp4",
                        thumbnail = "" // Empty
                    ),
                    AnimationItem(
                        isPremium = false,
                        mediaOriginal = "https://example.com/anime4.mp4",
                        thumbnail = "https://example.com/anime4_thumb.png" // Valid
                    )
                )
            )
        )
        
        coEvery { mockAnimationDataService.getAllAnimationCategories() } returns categoriesWithInvalidThumbnails
        
        // Act
        val actualThumbnails = thumbnailDataService.getThumbnailsForPreloading()
        
        // Assert
        assertEquals("Should return only valid thumbnails", 2, actualThumbnails.size)
        assertTrue("All returned thumbnails should have valid URLs", 
            actualThumbnails.all { it.thumbnailUrl.startsWith("https://") })
    }
    
    @Test
    fun `getThumbnailsForPreloading handles case-insensitive category matching`() = runTest {
        // Arrange
        val categoriesWithDifferentCase = listOf(
            AnimationCategory(
                name = "anime", // lowercase
                content = listOf(
                    AnimationItem(
                        isPremium = false,
                        mediaOriginal = "https://example.com/anime1.mp4",
                        thumbnail = "https://example.com/anime1_thumb.jpg"
                    )
                )
            ),
            AnimationCategory(
                name = "CARTOON", // uppercase
                content = listOf(
                    AnimationItem(
                        isPremium = false,
                        mediaOriginal = "https://example.com/cartoon1.mp4",
                        thumbnail = "https://example.com/cartoon1_thumb.jpg"
                    )
                )
            )
        )
        
        coEvery { mockAnimationDataService.getAllAnimationCategories() } returns categoriesWithDifferentCase
        
        // Act
        val actualThumbnails = thumbnailDataService.getThumbnailsForPreloading()
        
        // Assert
        assertEquals("Should match categories case-insensitively", 2, actualThumbnails.size)
        assertTrue("Should include lowercase anime category", 
            actualThumbnails.any { it.categoryName == "anime" })
        assertTrue("Should include uppercase cartoon category", 
            actualThumbnails.any { it.categoryName == "CARTOON" })
    }
    
    @Test
    fun `getAvailableCategories returns all category names`() = runTest {
        // Arrange
        coEvery { mockAnimationDataService.getAllAnimationCategories() } returns testCategories
        
        // Act
        val actualCategories = thumbnailDataService.getAvailableCategories()
        
        // Assert
        assertEquals("Should return all category names", 3, actualCategories.size)
        assertTrue("Should contain Anime", actualCategories.contains("Anime"))
        assertTrue("Should contain Cartoon", actualCategories.contains("Cartoon"))
        assertTrue("Should contain Other", actualCategories.contains("Other"))
    }
    
    @Test
    fun `areTargetCategoriesAvailable returns true when target categories exist`() = runTest {
        // Arrange
        coEvery { mockAnimationDataService.getAllAnimationCategories() } returns testCategories
        
        // Act
        val actualResult = thumbnailDataService.areTargetCategoriesAvailable()
        
        // Assert
        assertTrue("Should return true when Anime and Cartoon categories are available", actualResult)
    }
    
    @Test
    fun `areTargetCategoriesAvailable returns false when no target categories exist`() = runTest {
        // Arrange
        val categoriesWithoutTargets = listOf(
            AnimationCategory(
                name = "Other",
                content = listOf(
                    AnimationItem(
                        isPremium = false,
                        mediaOriginal = "https://example.com/other1.mp4",
                        thumbnail = "https://example.com/other1_thumb.jpg"
                    )
                )
            )
        )
        
        coEvery { mockAnimationDataService.getAllAnimationCategories() } returns categoriesWithoutTargets
        
        // Act
        val actualResult = thumbnailDataService.areTargetCategoriesAvailable()
        
        // Assert
        assertFalse("Should return false when no target categories are available", actualResult)
    }
    
    @Test
    fun `getThumbnailStatistics returns correct statistics`() = runTest {
        // Arrange
        coEvery { mockAnimationDataService.getAllAnimationCategories() } returns testCategories
        
        // Act
        val actualStats = thumbnailDataService.getThumbnailStatistics()
        
        // Assert
        assertEquals("Should have correct total count", 8, actualStats["total"])
        assertEquals("Should have correct premium count", 2, actualStats["premium"])
        assertEquals("Should have correct free count", 6, actualStats["free"])
        assertEquals("Should have correct Anime category count", 4, actualStats["category_Anime"])
        assertEquals("Should have correct Cartoon category count", 4, actualStats["category_Cartoon"])
        assertNull("Should not have Other category in stats", actualStats["category_Other"])
    }
    
    @Test
    fun `getThumbnailsForPreloading calls AnimationDataService correctly`() = runTest {
        // Arrange
        coEvery { mockAnimationDataService.getAllAnimationCategories() } returns testCategories
        
        // Act
        thumbnailDataService.getThumbnailsForPreloading()
        
        // Assert
        coVerify(exactly = 1) { mockAnimationDataService.getAllAnimationCategories() }
    }
}
