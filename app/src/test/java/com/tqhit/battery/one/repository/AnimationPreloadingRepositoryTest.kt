package com.tqhit.battery.one.repository

import com.google.gson.Gson
import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import com.tqhit.battery.one.fragment.main.animation.data.AnimationItem
import com.tqhit.battery.one.fragment.main.animation.data.PreloadResult
import com.tqhit.battery.one.fragment.main.animation.data.PreloadedAnimationItem
import com.tqhit.battery.one.fragment.main.animation.data.PreloadStatus
import com.tqhit.battery.one.manager.animation.AnimationFileManager
import com.tqhit.battery.one.service.animation.AnimationPreloader
import com.tqhit.battery.one.service.animation.PreloadingStatus
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import java.io.File

/**
 * Unit tests for AnimationPreloadingRepository.
 * Tests repository pattern implementation, data persistence, and coordination logic.
 * 
 * Following Arrange-Act-Assert convention for test structure.
 */
class AnimationPreloadingRepositoryTest {
    
    // Test dependencies
    private lateinit var mockPreferencesHelper: PreferencesHelper
    private lateinit var mockAnimationPreloader: AnimationPreloader
    private lateinit var mockFileManager: AnimationFileManager
    private lateinit var mockGson: Gson
    private lateinit var animationPreloadingRepository: AnimationPreloadingRepository
    
    // Test data
    private lateinit var testAnimations: List<AnimationItem>
    private lateinit var testPreloadedItem: PreloadedAnimationItem
    
    @Before
    fun setUp() {
        // Arrange - Set up mocks and test subject
        mockPreferencesHelper = mockk(relaxed = true)
        mockAnimationPreloader = mockk(relaxed = true)
        mockFileManager = mockk(relaxed = true)
        mockGson = mockk(relaxed = true)
        
        animationPreloadingRepository = AnimationPreloadingRepository(
            mockPreferencesHelper,
            mockAnimationPreloader,
            mockFileManager,
            mockGson
        )
        
        // Create test data
        testAnimations = listOf(
            AnimationItem(
                isPremium = false,
                mediaOriginal = "https://example.com/animation1.mp4",
                thumbnail = "https://example.com/thumb1.jpg"
            ),
            AnimationItem(
                isPremium = true,
                mediaOriginal = "https://example.com/animation2.mp4",
                thumbnail = "https://example.com/thumb2.jpg"
            )
        )
        
        testPreloadedItem = PreloadedAnimationItem(
            mediaOriginal = "https://example.com/animation1.mp4",
            localFilePath = "/mock/path/animation1.mp4",
            status = PreloadStatus.COMPLETED,
            downloadTimestamp = System.currentTimeMillis(),
            fileSizeBytes = 2048L
        )
    }
    
    @Test
    fun `initiatePreloading should return NoAnimationsProvided for empty list`() = runTest {
        // Arrange
        val emptyAnimations = emptyList<AnimationItem>()
        
        // Act
        val actualResult = animationPreloadingRepository.initiatePreloading(emptyAnimations)
        
        // Assert
        assertTrue("Should return NoAnimationsProvided", actualResult is PreloadingResult.NoAnimationsProvided)
    }
    
    @Test
    fun `initiatePreloading should return AlreadyUpToDate when not needed`() = runTest {
        // Arrange - Mock recent preload timestamp
        val recentTimestamp = System.currentTimeMillis() - (1000 * 60 * 60) // 1 hour ago
        every { mockPreferencesHelper.getLong("last_preload_timestamp", 0L) } returns recentTimestamp
        every { mockPreferencesHelper.getInt("preload_version", 0) } returns 1
        
        // Act
        val actualResult = animationPreloadingRepository.initiatePreloading(testAnimations)
        
        // Assert
        assertTrue("Should return AlreadyUpToDate", actualResult is PreloadingResult.AlreadyUpToDate)
    }
    
    @Test
    fun `initiatePreloading should return Success for successful preloading`() = runTest {
        // Arrange
        every { mockPreferencesHelper.getLong("last_preload_timestamp", 0L) } returns 0L
        every { mockPreferencesHelper.getInt("preload_version", 0) } returns 0
        
        val successResults = listOf(
            PreloadResult.Success(testPreloadedItem),
            PreloadResult.AlreadyExists(testPreloadedItem)
        )
        coEvery { mockAnimationPreloader.preloadAnimations(testAnimations) } returns successResults
        coEvery { mockFileManager.cleanupOldFiles() } returns 2
        
        every { mockGson.toJson(any<List<PreloadedAnimationItem>>()) } returns "mock_json"
        
        // Act
        val actualResult = animationPreloadingRepository.initiatePreloading(testAnimations)
        
        // Assert
        assertTrue("Should return Success", actualResult is PreloadingResult.Success)
        val successResult = actualResult as PreloadingResult.Success
        assertEquals("Should have correct preloaded count", 2, successResult.preloadedCount)
        
        // Verify interactions
        verify { mockPreferencesHelper.saveString("preloaded_animations", "mock_json") }
        verify { mockPreferencesHelper.saveLong("last_preload_timestamp", any()) }
    }
    
    @Test
    fun `initiatePreloading should return AllFailed for all failures`() = runTest {
        // Arrange
        every { mockPreferencesHelper.getLong("last_preload_timestamp", 0L) } returns 0L
        every { mockPreferencesHelper.getInt("preload_version", 0) } returns 0
        
        val failureResults = listOf(
            PreloadResult.Failure("https://example.com/animation1.mp4", "Network error"),
            PreloadResult.Failure("https://example.com/animation2.mp4", "Invalid file")
        )
        coEvery { mockAnimationPreloader.preloadAnimations(testAnimations) } returns failureResults
        coEvery { mockFileManager.cleanupOldFiles() } returns 0
        
        // Act
        val actualResult = animationPreloadingRepository.initiatePreloading(testAnimations)
        
        // Assert
        assertTrue("Should return AllFailed", actualResult is PreloadingResult.AllFailed)
        val failedResult = actualResult as PreloadingResult.AllFailed
        assertEquals("Should have correct failure count", 2, failedResult.failures.size)
    }
    
    @Test
    fun `initiatePreloading should return PartialSuccess for mixed results`() = runTest {
        // Arrange
        every { mockPreferencesHelper.getLong("last_preload_timestamp", 0L) } returns 0L
        every { mockPreferencesHelper.getInt("preload_version", 0) } returns 0
        
        val mixedResults = listOf(
            PreloadResult.Success(testPreloadedItem),
            PreloadResult.Failure("https://example.com/animation2.mp4", "Network error")
        )
        coEvery { mockAnimationPreloader.preloadAnimations(testAnimations) } returns mixedResults
        coEvery { mockFileManager.cleanupOldFiles() } returns 1
        
        every { mockGson.toJson(any<List<PreloadedAnimationItem>>()) } returns "mock_json"
        
        // Act
        val actualResult = animationPreloadingRepository.initiatePreloading(testAnimations)
        
        // Assert
        assertTrue("Should return PartialSuccess", actualResult is PreloadingResult.PartialSuccess)
        val partialResult = actualResult as PreloadingResult.PartialSuccess
        assertEquals("Should have correct success count", 1, partialResult.successCount)
        assertEquals("Should have correct failure count", 1, partialResult.failures.size)
    }
    
    @Test
    fun `getPreloadedAnimation should return item from file system`() = runTest {
        // Arrange
        val mediaUrl = "https://example.com/animation1.mp4"
        coEvery { mockFileManager.getPreloadedFile(mediaUrl) } returns testPreloadedItem
        
        // Act
        val actualResult = animationPreloadingRepository.getPreloadedAnimation(mediaUrl)
        
        // Assert
        assertNotNull("Should return preloaded item", actualResult)
        assertEquals("Should return correct item", testPreloadedItem, actualResult)
        coVerify { mockFileManager.getPreloadedFile(mediaUrl) }
    }
    
    @Test
    fun `getPreloadedAnimation should return null when not found`() = runTest {
        // Arrange
        val mediaUrl = "https://example.com/nonexistent.mp4"
        coEvery { mockFileManager.getPreloadedFile(mediaUrl) } returns null
        every { mockPreferencesHelper.getString("preloaded_animations", "") } returns ""
        
        // Act
        val actualResult = animationPreloadingRepository.getPreloadedAnimation(mediaUrl)
        
        // Assert
        assertNull("Should return null when not found", actualResult)
    }
    
    @Test
    fun `getPreloadedAnimation should fallback to preferences when file system fails`() = runTest {
        // Arrange
        val mediaUrl = "https://example.com/animation1.mp4"
        val mockFile = mockk<File>(relaxed = true)
        
        coEvery { mockFileManager.getPreloadedFile(mediaUrl) } returns null
        every { mockPreferencesHelper.getString("preloaded_animations", "") } returns "mock_json"
        every { mockGson.fromJson<List<PreloadedAnimationItem>>(any<String>(), any<java.lang.reflect.Type>()) } returns listOf(testPreloadedItem)
        every { mockFile.exists() } returns true
        every { mockFile.length() } returns 2048L
        
        // Mock File constructor
        every { File(testPreloadedItem.localFilePath) } returns mockFile
        
        // Act
        val actualResult = animationPreloadingRepository.getPreloadedAnimation(mediaUrl)
        
        // Assert
        assertNotNull("Should return preloaded item from preferences", actualResult)
        assertEquals("Should return correct item", testPreloadedItem, actualResult)
    }
    
    @Test
    fun `getPreloadingStats should return correct statistics`() = runTest {
        // Arrange
        val expectedFileCount = 5
        val expectedTotalSize = 10240L
        val expectedTimestamp = System.currentTimeMillis()
        
        coEvery { mockFileManager.getPreloadedFileCount() } returns expectedFileCount
        coEvery { mockFileManager.getTotalPreloadedSize() } returns expectedTotalSize
        every { mockPreferencesHelper.getLong("last_preload_timestamp", 0L) } returns expectedTimestamp
        
        // Act
        val actualStats = animationPreloadingRepository.getPreloadingStats()
        
        // Assert
        assertEquals("Should return correct file count", expectedFileCount, actualStats.preloadedFileCount)
        assertEquals("Should return correct total size", expectedTotalSize, actualStats.totalSizeBytes)
        assertEquals("Should return correct timestamp", expectedTimestamp, actualStats.lastPreloadTimestamp)
    }
    
    @Test
    fun `clearAllPreloadedData should clear preferences and files`() = runTest {
        // Arrange
        coEvery { mockFileManager.cleanupOldFiles() } returns 3
        
        // Act
        val actualResult = animationPreloadingRepository.clearAllPreloadedData()
        
        // Assert
        assertTrue("Should return true for successful clear", actualResult)
        
        // Verify interactions
        verify { mockPreferencesHelper.saveString("preloaded_animations", "") }
        verify { mockPreferencesHelper.saveLong("last_preload_timestamp", 0L) }
        verify { mockPreferencesHelper.saveInt("preload_version", 0) }
        coVerify { mockFileManager.cleanupOldFiles() }
    }
    
    @Test
    fun `clearAllPreloadedData should handle exceptions gracefully`() = runTest {
        // Arrange
        coEvery { mockFileManager.cleanupOldFiles() } throws RuntimeException("File system error")
        
        // Act
        val actualResult = animationPreloadingRepository.clearAllPreloadedData()
        
        // Assert
        assertFalse("Should return false when exception occurs", actualResult)
    }
}
